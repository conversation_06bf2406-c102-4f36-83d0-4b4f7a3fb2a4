import Flutter
import UIKit
import UserNotifications

public class AwesomeNotificationsServicePlugin: NSObject, FlutterPlugin {

    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "awesome_notifications_service", binaryMessenger: registrar.messenger())
        let instance = AwesomeNotificationsServicePlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }

    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "initialize":
            initializeNotifications(result: result)
        case "requestPermissions":
            requestNotificationPermissions(result: result)
        case "checkPermissions":
            checkNotificationPermissions(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }

    private func initializeNotifications(result: @escaping FlutterResult) {
        // Initialize notification categories and actions
        setupNotificationCategories()
        result(true)
    }

    private func requestNotificationPermissions(result: @escaping FlutterResult) {
        let center = UNUserNotificationCenter.current()

        var options: UNAuthorizationOptions = [
            .alert,
            .badge,
            .sound,
            .provisional
        ]

        // Add critical alerts if available (iOS 12+)
        if #available(iOS 12.0, *) {
            options.insert(.criticalAlert)
        }

        // Add time sensitive if available (iOS 15+)
        if #available(iOS 15.0, *) {
            options.insert(.timeSensitive)
        }

        center.requestAuthorization(options: options) { granted, error in
            DispatchQueue.main.async {
                if let error = error {
                    result(FlutterError(code: "PERMISSION_ERROR",
                                      message: error.localizedDescription,
                                      details: nil))
                } else {
                    result(granted)
                }
            }
        }
    }

    private func checkNotificationPermissions(result: @escaping FlutterResult) {
        let center = UNUserNotificationCenter.current()

        center.getNotificationSettings { settings in
            DispatchQueue.main.async {
                let status = settings.authorizationStatus
                let granted = status == .authorized || status == .provisional
                result(granted)
            }
        }
    }

    private func setupNotificationCategories() {
        let center = UNUserNotificationCenter.current()

        // Prayer notification category
        let dismissAction = UNNotificationAction(
            identifier: "DISMISS_ACTION",
            title: "إلغاء",
            options: []
        )

        let stopAction = UNNotificationAction(
            identifier: "STOP_ACTION",
            title: "إيقاف الكل",
            options: [.destructive]
        )

        let prayerCategory = UNNotificationCategory(
            identifier: "PRAYER_CATEGORY",
            actions: [dismissAction, stopAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Athkar notification category
        let athkarCategory = UNNotificationCategory(
            identifier: "ATHKAR_CATEGORY",
            actions: [dismissAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Dhikr notification category
        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE_ACTION",
            title: "تأجيل",
            options: []
        )

        let dhikrCategory = UNNotificationCategory(
            identifier: "DHIKR_CATEGORY",
            actions: [dismissAction, snoozeAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Register categories
        center.setNotificationCategories([
            prayerCategory,
            athkarCategory,
            dhikrCategory
        ])
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension AwesomeNotificationsServicePlugin: UNUserNotificationCenterDelegate {

    public func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.alert, .badge, .sound])
    }

    public func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // Handle notification actions
        let actionIdentifier = response.actionIdentifier
        let notification = response.notification

        switch actionIdentifier {
        case "DISMISS_ACTION":
            // Just dismiss the notification
            break
        case "STOP_ACTION":
            // Cancel all notifications of the same type
            cancelNotificationsByType(notification: notification)
        case "SNOOZE_ACTION":
            // Reschedule notification for later
            snoozeNotification(notification: notification)
        default:
            // Default action (tap on notification)
            break
        }

        completionHandler()
    }

    private func cancelNotificationsByType(notification: UNNotification) {
        let center = UNUserNotificationCenter.current()
        let userInfo = notification.request.content.userInfo

        if let payload = userInfo["payload"] as? String {
            center.getPendingNotificationRequests { requests in
                let identifiersToCancel = requests.compactMap { request in
                    if let requestPayload = request.content.userInfo["payload"] as? String,
                       requestPayload.contains(payload) {
                        return request.identifier
                    }
                    return nil
                }

                center.removePendingNotificationRequests(withIdentifiers: identifiersToCancel)
            }
        }
    }

    private func snoozeNotification(notification: UNNotification) {
        let center = UNUserNotificationCenter.current()
        let content = notification.request.content.mutableCopy() as! UNMutableNotificationContent

        // Schedule for 5 minutes later
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 300, repeats: false)
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: trigger
        )

        center.add(request) { error in
            if let error = error {
                print("Error snoozing notification: \(error)")
            }
        }
    }
}
