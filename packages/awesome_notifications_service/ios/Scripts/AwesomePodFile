#!/usr/bin/env ruby

# AwesomePodFile for awesome_notifications_service
# This script provides the necessary pod modifications for awesome_notifications integration

def update_awesome_pod_build_settings(installer)
  puts "🔧 Updating awesome_notifications_service pod build settings..."

  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Set minimum deployment target for all targets
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'

      # Configure build settings for awesome_notifications and IosAwnCore specifically
      if target.name.include?('awesome_notifications') || target.name.include?('IosAwnCore')
        # Enable modules for awesome_notifications
        config.build_settings['DEFINES_MODULE'] = 'YES'

        # Disable bitcode (deprecated in Xcode 14)
        config.build_settings['ENABLE_BITCODE'] = 'NO'

        # Set valid architectures
        config.build_settings['VALID_ARCHS'] = 'arm64 x86_64'

        # Exclude simulator architectures for device builds
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'i386'

        # Additional settings for awesome_notifications compatibility
        config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'NO'
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'

        # CRITICAL: Fix static linking issue for awesome_notifications and IosAwnCore
        # config.build_settings['MACH_O_TYPE'] = 'mh_dylib'
        # config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'NO'
        config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
        config.build_settings['SWIFT_VERSION'] = '5.0'

        puts "  ✅ Updated #{target.name} build settings (static linking resolved)"
      end

      # Special handling for Pods-Runner target to resolve transitive dependencies
      if target.name == 'Pods-Runner'
        config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'YES'
        config.build_settings['LD_RUNPATH_SEARCH_PATHS'] = [
          '$(inherited)',
          '@executable_path/Frameworks',
          '@loader_path/Frameworks'
        ]
        puts "  ✅ Updated Pods-Runner target for transitive dependencies"
      end
    end
  end
end

def update_awesome_main_target_settings(target_name, project_path, flutter_root)
  puts "🔧 Updating #{target_name} target settings for awesome_notifications_service..."
  
  # Read the project file
  project_file = File.join(project_path, "#{target_name}.xcodeproj", "project.pbxproj")
  
  if File.exist?(project_file)
    project_content = File.read(project_file)
    
    # Ensure minimum deployment target
    if project_content.include?('IPHONEOS_DEPLOYMENT_TARGET')
      project_content.gsub!(/IPHONEOS_DEPLOYMENT_TARGET = [0-9]+\.[0-9]+;/, 'IPHONEOS_DEPLOYMENT_TARGET = 12.0;')
      puts "  ✅ Updated iOS deployment target to 12.0"
    end

    # Fix static linking issues in main target
    unless project_content.include?('ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES')
      # Add Swift standard libraries embedding for main target
      project_content.gsub!(
        /(buildSettings = \{[^}]*)(ENABLE_BITCODE = [^;]*;)/,
        '\1ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;' + "\n\t\t\t\t" + '\2'
      )
      puts "  ✅ Added Swift standard libraries embedding"
    end

    # Write back the modified content
    File.write(project_file, project_content)
  else
    puts "  ⚠️  Project file not found: #{project_file}"
  end
  
  puts "  ✅ Main target settings updated (static linking resolved)"
end

puts "📱 AwesomePodFile for awesome_notifications_service loaded successfully"
