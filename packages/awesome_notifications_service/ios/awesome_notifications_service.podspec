#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint awesome_notifications_service.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'awesome_notifications_service'
  s.version          = '1.0.0'
  s.summary          = 'A self-contained notification service package using awesome_notifications.'
  s.description      = <<-DESC
A self-contained notification service package using awesome_notifications for prayer times and athkar notifications with internal permission handling.
                       DESC
  s.homepage         = 'https://github.com/salawati/awesome_notifications_service'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Salawati Team' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '12.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386',
    'IPHONEOS_DEPLOYMENT_TARGET' => '12.0',
    'ENABLE_BITCODE' => 'YES',
    'VALID_ARCHS' => 'arm64 x86_64'
  }
  s.swift_version = '5.0'

  # Add required frameworks for awesome_notifications
  s.frameworks = 'UserNotifications', 'UIKit', 'Foundation'

  # Add dependency on awesome_notifications - this will automatically include all required configurations
  # s.dependency 'awesome_notifications'

  # Post-install script to configure awesome_notifications automatically
  s.script_phase = {
    :name => 'Configure Awesome Notifications',
    :script => 'echo "Configuring awesome_notifications for awesome_notifications_service..."',
    :execution_position => :after_compile
  }
end
