# Awesome Notifications Service

A completely self-contained notification service package that wraps awesome_notifications with **zero-configuration setup**. Simply add to dependencies and start using - no manual platform configuration required.

## ✨ Key Features

- **🔌 Plug-and-Play**: Zero manual configuration required
- **📱 Cross-Platform**: Works on Android and iOS automatically
- **🔧 Self-Contained**: All permissions, services, and configurations handled internally
- **🎯 Prayer-Focused**: Optimized for prayer times and athkar notifications
- **🛡️ Permission Management**: Automatic permission handling with user-friendly flows
- **🔄 Hybrid Operation**: Seamless fallback to legacy systems during migration
- **🌍 Localization**: Arabic and English support
- **🔥 Firebase Ready**: Built-in Firebase push notification support

## 🔧 What's Handled Automatically

### Android
- ✅ All required permissions (`VIBRATE`, `WAKE_LOCK`, `POST_NOTIFICATIONS`, etc.)
- ✅ Boot receivers for notification rescheduling
- ✅ Foreground services for critical notifications
- ✅ Exact alarm permissions for Android 12+
- ✅ Notification channels and groups
- ✅ Background processing services

### iOS
- ✅ UserNotifications framework integration
- ✅ Background app refresh capabilities
- ✅ Notification categories and actions
- ✅ Sound and badge management
- ✅ iOS 12+ compatibility
- ✅ Podfile configurations

## 📋 No Manual Configuration Required

Unlike other notification packages, you **DO NOT** need to:

- ❌ Modify `AndroidManifest.xml`
- ❌ Update iOS `Podfile`
- ❌ Add permissions manually
- ❌ Configure notification channels
- ❌ Set up boot receivers
- ❌ Handle platform-specific code

Everything is handled automatically by the plugin's internal configuration system.

## Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  awesome_notifications_service:
    path: packages/awesome_notifications_service
```

## Usage

### Initialization

Initialize the service once during app startup:

```dart
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the notification service
  await AwesomeNotificationsService.initialize();

  runApp(MyApp());
}
```

### Request Permissions

Request all required permissions:

```dart
// Request permissions
final hasPermissions = await AwesomeNotificationsService.requestPermissions();

if (!hasPermissions) {
  // Handle permission denied
  await AwesomeNotificationsService.openNotificationSettings();
}
```

### Prayer Notifications

Schedule prayer notifications:

```dart
// Create prayer times
final prayerTimes = PrayerTimes(
  fajr: DateTime(2024, 1, 1, 5, 30),
  sunrise: DateTime(2024, 1, 1, 7, 0),
  dhuhr: DateTime(2024, 1, 1, 12, 15),
  asr: DateTime(2024, 1, 1, 15, 30),
  maghrib: DateTime(2024, 1, 1, 18, 0),
  isha: DateTime(2024, 1, 1, 19, 30),
);

// Configure notifications for each prayer
final notificationConfigs = {
  'Fajr': PrayerNotificationConfig(
    prayerName: 'Fajr',
    isNotified: true,
    isPreNotified: true,
    iqamahMinutes: 10,
    soundIndex: 0,
  ),
  // ... configure other prayers
};

// Schedule notifications
await AwesomeNotificationsService.schedulePrayerNotifications(
  prayerTimes: prayerTimes,
  notificationConfigs: notificationConfigs,
  daysAhead: 7,
  locale: 'ar',
);
```

### Athkar Notifications

Schedule morning and evening athkar:

```dart
// Morning athkar
final morningConfig = AthkarNotificationConfig(
  type: AthkarType.morning,
  isEnabled: true,
  time: '06:00',
);

await AwesomeNotificationsService.scheduleMorningAthkar(
  config: morningConfig,
  locale: 'ar',
);

// Evening athkar
final eveningConfig = AthkarNotificationConfig(
  type: AthkarType.evening,
  isEnabled: true,
  time: '18:00',
);

await AwesomeNotificationsService.scheduleEveningAthkar(
  config: eveningConfig,
  locale: 'ar',
);
```

### Dhikr Reminders

Schedule dhikr reminders:

```dart
final dhikrConfig = AthkarNotificationConfig(
  type: AthkarType.dhikr,
  isEnabled: true,
  time: '08:00',
  dhikrItems: PredefinedDhikr.items,
  intervalMinutes: 120, // Every 2 hours
);

await AwesomeNotificationsService.scheduleDhikrReminders(
  config: dhikrConfig,
  locale: 'ar',
);
```

### Firebase Notifications

Handle Firebase messages:

```dart
// In your Firebase messaging setup
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  AwesomeNotificationsService.handleForegroundFirebaseMessage(
    message.toMap(),
  );
});

FirebaseMessaging.onBackgroundMessage((RemoteMessage message) async {
  await AwesomeNotificationsService.handleBackgroundFirebaseMessage(
    message.toMap(),
  );
});
```

### Cancel Notifications

```dart
// Cancel all prayer notifications
await AwesomeNotificationsService.cancelAllPrayerNotifications();

// Cancel specific prayer notifications
await AwesomeNotificationsService.cancelPrayerNotifications('Fajr');

// Cancel all athkar notifications
await AwesomeNotificationsService.cancelAllAthkarNotifications();

// Cancel all notifications
await AwesomeNotificationsService.cancelAllNotifications();
```

### Check Notification Status

```dart
// Check if notifications are scheduled
final hasPrayer = await AwesomeNotificationsService.hasPrayerNotifications();
final hasAthkar = await AwesomeNotificationsService.hasAthkarNotifications();

// Get notification counts
final stats = await AwesomeNotificationsService.getNotificationStatistics();
print('Total notifications: ${stats.totalNotifications}');
print('Prayer notifications: ${stats.prayerNotifications}');
print('Athkar notifications: ${stats.athkarNotifications}');
```

## Configuration

### Android

The package automatically configures Android permissions and receivers. No additional setup required.

### iOS

The package automatically configures iOS notification categories and actions. No additional setup required.

## Sound Files

The package expects sound files to be placed in the `android/app/src/main/res/raw/` directory for Android and in the iOS bundle for iOS. The following sound files are supported:

- `athan1.wav`, `athan2.wav`, `athan3.wav`, `athan4.wav` - Athan sounds
- `athan1_short.wav`, `athan2_short.wav`, etc. - Short athan sounds
- `bird.wav` - Sunrise sound
- `iqama.wav` - Iqamah sound
- `pre_athan.wav` - Pre-athan warning sound
- `sou_*.wav` - Dhikr sounds

## Permissions

The package handles all required permissions internally:

- **Android**: `POST_NOTIFICATIONS`, `SCHEDULE_EXACT_ALARM`, `USE_EXACT_ALARM`, `WAKE_LOCK`, `VIBRATE`
- **iOS**: Alert, Badge, Sound, Critical Alerts

## Architecture

The package is designed with a modular architecture:

- **AwesomeNotificationsManager**: Core notification management
- **PermissionService**: Internal permission handling
- **PrayerNotificationService**: Prayer-specific notifications
- **AthkarNotificationService**: Athkar and dhikr notifications
- **FirebaseNotificationService**: Firebase integration
- **AwesomeNotificationsService**: Main public API

## License

This package is part of the Salawati app and is proprietary software.
