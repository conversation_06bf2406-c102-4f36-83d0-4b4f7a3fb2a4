name: awesome_notifications_service
description: A completely self-contained notification service package that wraps awesome_notifications with zero-configuration setup. Simply add to dependencies and start using - no manual platform configuration required.
version: 1.0.0

environment:
  sdk: '>=3.5.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

    # Core notification library - Commented out to use main app's dependency
  # awesome_notifications: ^0.10.1

  # Permission handling
  permission_handler: ^12.0.0+1

  # Device information
  device_info_plus: ^11.5.0
  awesome_notifications_core: ^0.10.0
  awesome_notifications: ^0.10.1

  # Timezone support
  timezone: ^0.10.1
  flutter_timezone: ^4.1.0

  # Utilities
  get: ^4.7.2
  get_storage: ^2.1.1
  logger: ^2.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.salawati.awesome_notifications_service
        pluginClass: AwesomeNotificationsServicePlugin
      ios:
        pluginClass: AwesomeNotificationsServicePlugin