import 'package:flutter_test/flutter_test.dart';
import 'package:awesome_notifications_service/src/utils/notification_utils.dart';
import 'package:awesome_notifications_service/src/constants/notification_constants.dart';

void main() {
  group('NotificationUtils.generateNotificationId', () {
    test('should generate unique IDs for same prayer on different dates', () {
      // Test data: same prayer at same time on different dates
      final baseTime = DateTime(2025, 6, 2, 7, 30); // June 2, 2025 at 7:30 AM
      
      final ids = <int>[];
      
      // Generate IDs for the same prayer (Fajr main) over 7 days
      for (int day = 0; day < 7; day++) {
        final scheduledTime = baseTime.add(Duration(days: day));
        final id = NotificationUtils.generateNotificationId(
          notificationType: NotificationConstants.prayerTime,
          identifier: 'Fajr_main',
          scheduledTime: scheduledTime,
        );
        
        print('Day $day: ${scheduledTime.toString()} -> ID: $id');
        ids.add(id);
      }
      
      // Verify all IDs are unique
      final uniqueIds = ids.toSet();
      expect(uniqueIds.length, equals(ids.length), 
        reason: 'All notification IDs should be unique. Found duplicates: ${ids.length - uniqueIds.length}');
    });

    test('should generate unique IDs for different prayer types on same date', () {
      final scheduledTime = DateTime(2025, 6, 2, 7, 30);
      
      final ids = <int>[];
      final types = ['main', 'pre', 'iqamah'];
      
      for (final type in types) {
        final id = NotificationUtils.generateNotificationId(
          notificationType: NotificationConstants.prayerTime,
          identifier: 'Fajr_$type',
          scheduledTime: scheduledTime,
        );
        
        print('Type $type: ID: $id');
        ids.add(id);
      }
      
      // Verify all IDs are unique
      final uniqueIds = ids.toSet();
      expect(uniqueIds.length, equals(ids.length), 
        reason: 'All notification IDs should be unique for different types');
    });

    test('should generate unique IDs for different prayers on same date', () {
      final scheduledTime = DateTime(2025, 6, 2, 7, 30);
      
      final ids = <int>[];
      final prayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
      
      for (final prayer in prayers) {
        final id = NotificationUtils.generateNotificationId(
          notificationType: NotificationConstants.prayerTime,
          identifier: '${prayer}_main',
          scheduledTime: scheduledTime,
        );
        
        print('Prayer $prayer: ID: $id');
        ids.add(id);
      }
      
      // Verify all IDs are unique
      final uniqueIds = ids.toSet();
      expect(uniqueIds.length, equals(ids.length), 
        reason: 'All notification IDs should be unique for different prayers');
    });

    test('should generate consistent IDs for same parameters', () {
      final scheduledTime = DateTime(2025, 6, 2, 7, 30);
      
      final id1 = NotificationUtils.generateNotificationId(
        notificationType: NotificationConstants.prayerTime,
        identifier: 'Fajr_main',
        scheduledTime: scheduledTime,
      );
      
      final id2 = NotificationUtils.generateNotificationId(
        notificationType: NotificationConstants.prayerTime,
        identifier: 'Fajr_main',
        scheduledTime: scheduledTime,
      );
      
      expect(id1, equals(id2), 
        reason: 'Same parameters should generate the same ID');
    });
  });
}
