/// Awesome Notifications Service Package
///
/// A self-contained notification service package using awesome_notifications
/// for prayer times and athkar notifications with internal permission handling.
library;

// Core services
export 'src/services/awesome_notifications_manager.dart';
export 'src/services/permission_service.dart';
export 'src/services/prayer_notification_service.dart';
export 'src/services/athkar_notification_service.dart';
export 'src/services/firebase_notification_service.dart';

// Models
export 'src/models/notification_data.dart';
export 'src/models/prayer_notification.dart';
export 'src/models/athkar_notification.dart';

// Constants
export 'src/constants/notification_constants.dart';
export 'src/constants/notification_channels.dart';

// Utils
export 'src/utils/notification_utils.dart';
export 'src/utils/sound_utils.dart';

// Main service class
export 'src/awesome_notifications_service_main.dart';
