import 'package:logger/logger.dart';
import '../constants/notification_channels.dart';
import '../constants/notification_constants.dart';
import '../models/notification_data.dart';
import '../models/prayer_notification.dart';
import '../utils/notification_utils.dart';
import '../utils/sound_utils.dart';
import 'awesome_notifications_manager.dart';

/// Service for handling prayer time notifications
class PrayerNotificationService {
  static final PrayerNotificationService _instance =
      PrayerNotificationService._internal();
  factory PrayerNotificationService() => _instance;
  PrayerNotificationService._internal();

  final Logger _logger = Logger();
  final AwesomeNotificationsManager _notificationManager =
      AwesomeNotificationsManager();

  /// Schedule prayer notifications for multiple days
  Future<bool> schedulePrayerNotifications({
    required PrayerTimes prayerTimes,
    required Map<String, PrayerNotificationConfig> notificationConfigs,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    try {
      _logger.d('Scheduling prayer notifications for $daysAhead days');

      // Cancel existing prayer notifications first
      _logger.d('Cancelling existing prayer notifications...');
      final cancelResult = await cancelAllPrayerNotifications();
      _logger.d('Cancel result: $cancelResult');

      int scheduledCount = 0;
      final today = DateTime.now();

      // Schedule notifications for each day
      for (int dayOffset = 0; dayOffset < daysAhead; dayOffset++) {
        final targetDate = today.add(Duration(days: dayOffset));

        // Calculate prayer times for this date
        final dailyPrayerTimes =
            _adjustPrayerTimesForDate(prayerTimes, targetDate);

        // Schedule notifications for each prayer
        for (final prayerName in NotificationConstants.prayers) {
          final config = notificationConfigs[prayerName];
          if (config == null) continue;

          final prayerTime = dailyPrayerTimes.getPrayerTime(prayerName);
          if (prayerTime == null) continue;

          // Schedule main prayer notification
          if (config.isNotified) {
            _logger.d(
                'Scheduling main prayer notification for $prayerName at $prayerTime');
            final scheduled = await _schedulePrayerNotification(
              prayerName: prayerName,
              prayerTime: prayerTime,
              config: config,
              type: PrayerNotificationType.main,
              locale: locale,
            );
            if (scheduled) {
              scheduledCount++;
              _logger.d(
                  'Successfully scheduled main prayer notification for $prayerName');
            } else {
              _logger.w(
                  'Failed to schedule main prayer notification for $prayerName');
            }
          }

          // Schedule pre-prayer notification
          if (config.isPreNotified) {
            final prePrayerTime = prayerTime.subtract(const Duration(
                minutes: NotificationConstants.prePrayerWarningMinutes));

            if (NotificationUtils.isValidNotificationTime(prePrayerTime)) {
              final scheduled = await _schedulePrayerNotification(
                prayerName: prayerName,
                prayerTime: prePrayerTime,
                config: config,
                type: PrayerNotificationType.pre,
                locale: locale,
              );
              if (scheduled) scheduledCount++;
            }
          }

          // Schedule iqamah notification
          if (config.iqamahMinutes != null && config.iqamahMinutes! > 0) {
            final iqamahTime =
                prayerTime.add(Duration(minutes: config.iqamahMinutes!));

            if (NotificationUtils.isValidNotificationTime(iqamahTime)) {
              final scheduled = await _schedulePrayerNotification(
                prayerName: prayerName,
                prayerTime: iqamahTime,
                config: config,
                type: PrayerNotificationType.iqamah,
                locale: locale,
              );
              if (scheduled) scheduledCount++;
            }
          }
        }
      }

      _logger.i('Scheduled $scheduledCount prayer notifications');

      // Log final count for debugging
      final finalCount = await getScheduledPrayerNotificationsCount();
      _logger.d('📊 Total prayer notifications after scheduling: $finalCount');

      return scheduledCount > 0;
    } catch (e, stackTrace) {
      _logger.e('Error scheduling prayer notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Schedule a single prayer notification
  Future<bool> _schedulePrayerNotification({
    required String prayerName,
    required DateTime prayerTime,
    required PrayerNotificationConfig config,
    required PrayerNotificationType type,
    required String locale,
  }) async {
    try {
      // Generate notification ID
      final notificationId = NotificationUtils.generateNotificationId(
        notificationType: _getNotificationTypeString(type),
        identifier: '${prayerName}_${type.name}',
        scheduledTime: prayerTime,
      );

      // Check if notification with this ID already exists
      final existingNotifications =
          await _notificationManager.getScheduledNotifications();
      final alreadyExists = existingNotifications
          .any((notification) => notification.content?.id == notificationId);

      if (alreadyExists) {
        _logger
            .d('Notification with ID $notificationId already exists, skipping');
        return true; // Consider it successful since it already exists
      }

      // Debug logging for notification ID generation
      _logger.d('🔢 Generated notification ID: $notificationId');
      _logger.d('   - Prayer: $prayerName');
      _logger.d('   - Type: ${type.name}');
      _logger.d('   - Scheduled Time: $prayerTime');
      _logger.d('   - Identifier: ${prayerName}_${type.name}');

      // Get notification content
      final title =
          NotificationUtils.getLocalizedPrayerName(prayerName, locale: locale);
      final body = NotificationUtils.getLocalizedNotificationBody(
        _getNotificationTypeString(type),
        prayerName,
        locale: locale,
      );

      // Debug logging for notification content
      _logger.d('📝 Prayer notification content:');
      _logger.d('   - Prayer Name: $prayerName');
      _logger.d('   - Type: ${type.name}');
      _logger.d(
          '   - Notification Type String: ${_getNotificationTypeString(type)}');
      _logger.d('   - Locale: $locale');
      _logger.d('   - Generated Title: "$title"');
      _logger.d('   - Generated Body: "$body"');
      _logger.d('   - Prayer Time: $prayerTime');

      // Get sound path
      String? soundPath;
      if (type == PrayerNotificationType.main) {
        soundPath = SoundUtils.getPrayerSoundPath(
          prayerName: prayerName,
          soundIndex: config.soundIndex,
          useDefaultSound: config.useDefaultSound,
          isShort: true, // Use short athan sounds for notifications
        );
      } else if (type == PrayerNotificationType.pre) {
        soundPath = SoundUtils.getPreAthanSoundPath();
      } else if (type == PrayerNotificationType.iqamah) {
        soundPath = SoundUtils.getIqamaSoundPath();
      }

      // Create notification data
      final notificationData = PrayerNotificationData(
        id: notificationId,
        title: title,
        body: body,
        scheduledTime: prayerTime,
        channelKey: NotificationChannels.prayerChannel.channelKey!,
        notificationType: _getNotificationTypeString(type),
        prayerName: prayerName,
        prayerNotificationType: type,
        payload: NotificationConstants.athanNotificationsPayload,
        soundPath: soundPath,
        wakeUpScreen: type == PrayerNotificationType.main ||
            type == PrayerNotificationType.iqamah,
        fullScreenIntent: type == PrayerNotificationType.main ||
            type == PrayerNotificationType.iqamah,
        criticalAlert: type == PrayerNotificationType.main ||
            type == PrayerNotificationType.iqamah,
        actionButtons: _createPrayerActionButtons(locale),
        customData: {
          'prayer_name': prayerName,
          'prayer_type': type.name,
          'sound_index': config.soundIndex.toString(),
        },
      );

      _logger.d('📅 Scheduling notification with ID: $notificationId');
      // Schedule the notification
      final result =
          await _notificationManager.scheduleNotification(notificationData);
      _logger.d('✅ Notification scheduling result: $result');
      return result;
    } catch (e, stackTrace) {
      _logger.e('Error scheduling prayer notification',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel all prayer notifications
  Future<bool> cancelAllPrayerNotifications() async {
    try {
      await _notificationManager.cancelNotificationsByPayload(
          NotificationConstants.athanNotificationsPayload);
      _logger.d('All prayer notifications cancelled');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling prayer notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel notifications for specific prayer
  Future<bool> cancelPrayerNotifications(String prayerName) async {
    try {
      final scheduledNotifications =
          await _notificationManager.getScheduledNotifications();
      int cancelledCount = 0;

      for (final notification in scheduledNotifications) {
        final payload = notification.content?.payload?['payload'];
        if (payload == NotificationConstants.athanNotificationsPayload) {
          final notificationPrayerName =
              notification.content?.payload?['prayer_name'];
          if (notificationPrayerName == prayerName) {
            await _notificationManager
                .cancelNotification(notification.content!.id!);
            cancelledCount++;
          }
        }
      }

      _logger
          .d('Cancelled $cancelledCount notifications for prayer: $prayerName');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling prayer notifications for $prayerName',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Check if prayer notifications are scheduled
  Future<bool> hasPrayerNotifications() async {
    return await _notificationManager.hasScheduledNotification(
        NotificationConstants.athanNotificationsPayload);
  }

  /// Get scheduled prayer notifications count
  Future<int> getScheduledPrayerNotificationsCount() async {
    try {
      final notifications =
          await _notificationManager.getScheduledNotifications();
      return notifications.where((notification) {
        final payload = notification.content?.payload?['payload'];
        return payload == NotificationConstants.athanNotificationsPayload;
      }).length;
    } catch (e) {
      _logger.e('Error getting prayer notifications count: $e');
      return 0;
    }
  }

  /// Adjust prayer times for specific date
  PrayerTimes _adjustPrayerTimesForDate(
      PrayerTimes baseTimes, DateTime targetDate) {
    // This is a simplified implementation
    // In practice, you would recalculate prayer times for the specific date
    final daysDifference = targetDate.difference(DateTime.now()).inDays;

    return PrayerTimes(
      fajr: baseTimes.fajr.add(Duration(days: daysDifference)),
      sunrise: baseTimes.sunrise.add(Duration(days: daysDifference)),
      dhuhr: baseTimes.dhuhr.add(Duration(days: daysDifference)),
      asr: baseTimes.asr.add(Duration(days: daysDifference)),
      maghrib: baseTimes.maghrib.add(Duration(days: daysDifference)),
      isha: baseTimes.isha.add(Duration(days: daysDifference)),
      middleOfNight:
          baseTimes.middleOfNight?.add(Duration(days: daysDifference)),
      lastThirdOfNight:
          baseTimes.lastThirdOfNight?.add(Duration(days: daysDifference)),
    );
  }

  /// Get notification type string
  String _getNotificationTypeString(PrayerNotificationType type) {
    switch (type) {
      case PrayerNotificationType.main:
        return NotificationConstants.prayerTime;
      case PrayerNotificationType.pre:
        return NotificationConstants.prePrayerWarning;
      case PrayerNotificationType.iqamah:
        return NotificationConstants.iqamahTime;
    }
  }

  /// Create action buttons for prayer notifications
  List<NotificationActionButton> _createPrayerActionButtons(String locale) {
    return [
      NotificationActionButton(
        key: NotificationConstants.dismissAction,
        label: locale == 'ar' ? 'إلغاء' : 'Dismiss',
        autoDismissible: true,
      ),
      NotificationActionButton(
        key: NotificationConstants.stopAction,
        label: locale == 'ar' ? 'إيقاف الكل' : 'Stop All',
        isDangerousOption: true,
        autoDismissible: true,
      ),
    ];
  }

  /// Update prayer notification configuration
  Future<bool> updatePrayerNotificationConfig({
    required String prayerName,
    required PrayerNotificationConfig config,
    required PrayerTimes prayerTimes,
    String locale = 'ar',
  }) async {
    try {
      // Cancel existing notifications for this prayer
      await cancelPrayerNotifications(prayerName);

      // Reschedule with new configuration
      final configs = {prayerName: config};
      return await schedulePrayerNotifications(
        prayerTimes: prayerTimes,
        notificationConfigs: configs,
        daysAhead: NotificationConstants.maxNotificationDays,
        locale: locale,
      );
    } catch (e, stackTrace) {
      _logger.e('Error updating prayer notification config',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }
}
