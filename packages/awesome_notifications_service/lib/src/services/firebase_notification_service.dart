import 'dart:convert';
import 'package:logger/logger.dart';
import '../constants/notification_channels.dart';
import '../constants/notification_constants.dart';
import '../models/notification_data.dart';
import '../utils/notification_utils.dart';
import 'awesome_notifications_manager.dart';

/// Service for handling Firebase push notifications
class FirebaseNotificationService {
  static final FirebaseNotificationService _instance = FirebaseNotificationService._internal();
  factory FirebaseNotificationService() => _instance;
  FirebaseNotificationService._internal();

  final Logger _logger = Logger();
  final AwesomeNotificationsManager _notificationManager = AwesomeNotificationsManager();

  /// Initialize Firebase notification service
  Future<void> initialize() async {
    try {
      _logger.d('Initializing Firebase notification service');
      
      // Set up Firebase message handlers
      _setupFirebaseMessageHandlers();
      
      _logger.i('Firebase notification service initialized');
    } catch (e, stackTrace) {
      _logger.e('Error initializing Firebase notification service', error: e, stackTrace: stackTrace);
    }
  }

  /// Setup Firebase message handlers
  void _setupFirebaseMessageHandlers() {
    // Note: This is a placeholder for Firebase integration
    // In the actual implementation, you would import firebase_messaging
    // and set up the handlers here
    
    _logger.d('Firebase message handlers setup completed');
  }

  /// Handle foreground Firebase message
  Future<void> handleForegroundMessage(Map<String, dynamic> message) async {
    try {
      _logger.d('Handling foreground Firebase message: $message');

      final notification = _parseFirebaseMessage(message);
      if (notification != null) {
        // Show notification immediately for foreground messages
        await _showFirebaseNotification(notification);
      }

    } catch (e, stackTrace) {
      _logger.e('Error handling foreground message', error: e, stackTrace: stackTrace);
    }
  }

  /// Handle background Firebase message
  static Future<void> handleBackgroundMessage(Map<String, dynamic> message) async {
    try {
      final logger = Logger();
      logger.d('Handling background Firebase message: $message');

      final service = FirebaseNotificationService();
      final notification = service._parseFirebaseMessage(message);
      
      if (notification != null) {
        // Schedule notification for background messages
        await service._showFirebaseNotification(notification);
      }

    } catch (e, stackTrace) {
      final logger = Logger();
      logger.e('Error handling background message', error: e, stackTrace: stackTrace);
    }
  }

  /// Handle notification opened from Firebase
  Future<void> handleNotificationOpened(Map<String, dynamic> message) async {
    try {
      _logger.d('Handling notification opened: $message');

      // Extract action data from the message
      final action = message['action'];
      final data = message['data'] ?? {};

      // Handle different notification actions
      await _handleNotificationAction(action, data);

    } catch (e, stackTrace) {
      _logger.e('Error handling notification opened', error: e, stackTrace: stackTrace);
    }
  }

  /// Parse Firebase message to notification data
  NotificationData? _parseFirebaseMessage(Map<String, dynamic> message) {
    try {
      final notification = message['notification'];
      final data = message['data'] ?? {};

      if (notification == null) {
        _logger.w('Firebase message has no notification payload');
        return null;
      }

      final title = notification['title'] ?? 'إشعار';
      final body = notification['body'] ?? '';
      final notificationType = data['type'] ?? 'firebase_message';

      // Generate unique ID for Firebase notifications
      final notificationId = NotificationUtils.generateNotificationId(
        notificationType: notificationType,
        identifier: 'firebase_${DateTime.now().millisecondsSinceEpoch}',
      );

      return NotificationData(
        id: notificationId,
        title: title,
        body: body,
        scheduledTime: DateTime.now(),
        channelKey: NotificationChannels.firebaseChannel.channelKey!,
        notificationType: notificationType,
        payload: NotificationConstants.firebaseNotificationsPayload,
        customData: Map<String, String>.from(data),
        wakeUpScreen: _shouldWakeUpScreen(notificationType),
        fullScreenIntent: _shouldUseFullScreenIntent(notificationType),
        criticalAlert: _shouldUseCriticalAlert(notificationType),
        actionButtons: _createFirebaseActionButtons(data),
      );

    } catch (e, stackTrace) {
      _logger.e('Error parsing Firebase message', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Show Firebase notification
  Future<bool> _showFirebaseNotification(NotificationData notification) async {
    try {
      // For immediate notifications, we create them without scheduling
      return await _notificationManager.scheduleNotification(notification);

    } catch (e, stackTrace) {
      _logger.e('Error showing Firebase notification', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Handle notification action
  Future<void> _handleNotificationAction(String? action, Map<String, dynamic> data) async {
    try {
      switch (action) {
        case 'open_app':
          _logger.d('Opening app from notification');
          // Handle app opening logic
          break;
        
        case 'open_prayer_times':
          _logger.d('Opening prayer times from notification');
          // Handle prayer times navigation
          break;
        
        case 'open_athkar':
          _logger.d('Opening athkar from notification');
          // Handle athkar navigation
          break;
        
        case 'open_settings':
          _logger.d('Opening settings from notification');
          // Handle settings navigation
          break;
        
        default:
          _logger.d('Unknown notification action: $action');
          // Handle default action (usually open app)
          break;
      }

      // Log analytics event for notification interaction
      await _logNotificationInteraction(action, data);

    } catch (e, stackTrace) {
      _logger.e('Error handling notification action', error: e, stackTrace: stackTrace);
    }
  }

  /// Determine if notification should wake up screen
  bool _shouldWakeUpScreen(String notificationType) {
    switch (notificationType) {
      case 'urgent':
      case 'prayer_reminder':
      case 'breaking_news':
        return true;
      default:
        return false;
    }
  }

  /// Determine if notification should use full screen intent
  bool _shouldUseFullScreenIntent(String notificationType) {
    switch (notificationType) {
      case 'urgent':
      case 'emergency':
        return true;
      default:
        return false;
    }
  }

  /// Determine if notification should use critical alert
  bool _shouldUseCriticalAlert(String notificationType) {
    switch (notificationType) {
      case 'urgent':
      case 'emergency':
      case 'critical_update':
        return true;
      default:
        return false;
    }
  }

  /// Create action buttons for Firebase notifications
  List<NotificationActionButton>? _createFirebaseActionButtons(Map<String, dynamic> data) {
    final actions = data['actions'];
    if (actions == null) return null;

    try {
      final actionsList = jsonDecode(actions) as List<dynamic>;
      return actionsList.map((action) {
        return NotificationActionButton(
          key: action['key'] ?? 'default',
          label: action['label'] ?? 'OK',
          requiresUnlock: action['requiresUnlock'] ?? false,
          showsUserInterface: action['showsUserInterface'] ?? true,
          isDangerousOption: action['isDangerousOption'] ?? false,
          autoDismissible: action['autoDismissible'] ?? true,
        );
      }).toList();

    } catch (e) {
      _logger.w('Error parsing Firebase notification actions: $e');
      return null;
    }
  }

  /// Log notification interaction for analytics
  Future<void> _logNotificationInteraction(String? action, Map<String, dynamic> data) async {
    try {
      // Log to analytics service
      _logger.d('Notification interaction logged: action=$action, data=$data');
      
      // Here you would integrate with your analytics service
      // Example: FirebaseAnalytics.instance.logEvent(...)
      
    } catch (e) {
      _logger.w('Error logging notification interaction: $e');
    }
  }

  /// Send custom notification through Firebase
  Future<bool> sendCustomNotification({
    required String title,
    required String body,
    Map<String, String>? data,
    String? imageUrl,
    List<NotificationActionButton>? actionButtons,
  }) async {
    try {
      final notificationId = NotificationUtils.generateNotificationId(
        notificationType: 'custom_firebase',
        identifier: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      );

      final notification = NotificationData(
        id: notificationId,
        title: title,
        body: body,
        scheduledTime: DateTime.now(),
        channelKey: NotificationChannels.firebaseChannel.channelKey!,
        notificationType: 'custom_firebase',
        payload: NotificationConstants.firebaseNotificationsPayload,
        customData: data,
        actionButtons: actionButtons,
      );

      return await _showFirebaseNotification(notification);

    } catch (e, stackTrace) {
      _logger.e('Error sending custom notification', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get Firebase token (placeholder)
  Future<String?> getFirebaseToken() async {
    try {
      // This would be implemented with actual Firebase messaging
      _logger.d('Getting Firebase token');
      return 'placeholder_token';
    } catch (e) {
      _logger.e('Error getting Firebase token: $e');
      return null;
    }
  }

  /// Subscribe to Firebase topic
  Future<bool> subscribeToTopic(String topic) async {
    try {
      _logger.d('Subscribing to Firebase topic: $topic');
      // This would be implemented with actual Firebase messaging
      return true;
    } catch (e) {
      _logger.e('Error subscribing to topic: $e');
      return false;
    }
  }

  /// Unsubscribe from Firebase topic
  Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      _logger.d('Unsubscribing from Firebase topic: $topic');
      // This would be implemented with actual Firebase messaging
      return true;
    } catch (e) {
      _logger.e('Error unsubscribing from topic: $e');
      return false;
    }
  }

  /// Cancel all Firebase notifications
  Future<bool> cancelAllFirebaseNotifications() async {
    try {
      await _notificationManager.cancelNotificationsByPayload(
        NotificationConstants.firebaseNotificationsPayload
      );
      _logger.d('All Firebase notifications cancelled');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling Firebase notifications', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get scheduled Firebase notifications count
  Future<int> getScheduledFirebaseNotificationsCount() async {
    try {
      final notifications = await _notificationManager.getScheduledNotifications();
      return notifications.where((notification) {
        final payload = notification.content?.payload?['payload'];
        return payload == NotificationConstants.firebaseNotificationsPayload;
      }).length;
    } catch (e) {
      _logger.e('Error getting Firebase notifications count: $e');
      return 0;
    }
  }
}
