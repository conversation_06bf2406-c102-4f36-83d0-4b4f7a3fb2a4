import 'dart:io';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../constants/notification_constants.dart';

/// Service for handling all notification permissions internally
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  final Logger _logger = Logger();
  final GetStorage _storage = GetStorage();

  /// Initialize permission service
  Future<void> initialize() async {
    await GetStorage.init();
    _logger.d('PermissionService initialized');
  }

  /// Request all required notification permissions
  Future<PermissionResult> requestAllPermissions() async {
    try {
      _logger.d('Requesting all notification permissions');

      // Request basic notification permission
      final notificationResult = await _requestNotificationPermission();
      if (!notificationResult.granted) {
        return notificationResult;
      }

      // Request exact alarm permission on Android
      if (Platform.isAndroid) {
        final exactAlarmResult = await _requestExactAlarmPermission();
        if (!exactAlarmResult.granted) {
          _logger.w('Exact alarm permission not granted: ${exactAlarmResult.message}');
        } else {
          _logger.i('Exact alarm permission: ${exactAlarmResult.message}');
        }
      }

      // Request critical alerts permission on iOS
      if (Platform.isIOS) {
        final criticalAlertResult = await _requestCriticalAlertPermission();
        if (!criticalAlertResult.granted) {
          _logger.w('Critical alert permission not granted, but continuing');
        }
      }

      // Cache permission status
      await _cachePermissionStatus(true);

      return PermissionResult(
        type: PermissionType.notification,
        granted: true,
        message: 'All permissions granted successfully',
      );

    } catch (e, stackTrace) {
      _logger.e('Error requesting permissions', error: e, stackTrace: stackTrace);
      return PermissionResult(
        type: PermissionType.notification,
        granted: false,
        error: e.toString(),
      );
    }
  }

  /// Check current permission status
  Future<PermissionStatus> checkPermissionStatus() async {
    try {
      // Check awesome notifications permission
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();

      if (!isAllowed) {
        return PermissionStatus(
          hasNotificationPermission: false,
          hasExactAlarmPermission: false,
          hasCriticalAlertPermission: false,
          message: 'Notification permission not granted',
        );
      }

      // Check platform-specific permissions
      bool hasExactAlarm = true;
      bool hasCriticalAlert = true;

      if (Platform.isAndroid) {
        hasExactAlarm = await _checkExactAlarmPermission();
      }

      if (Platform.isIOS) {
        hasCriticalAlert = await _checkCriticalAlertPermission();
      }

      return PermissionStatus(
        hasNotificationPermission: true,
        hasExactAlarmPermission: hasExactAlarm,
        hasCriticalAlertPermission: hasCriticalAlert,
        message: 'Permissions checked successfully',
      );

    } catch (e, stackTrace) {
      _logger.e('Error checking permission status', error: e, stackTrace: stackTrace);
      return PermissionStatus(
        hasNotificationPermission: false,
        hasExactAlarmPermission: false,
        hasCriticalAlertPermission: false,
        error: e.toString(),
      );
    }
  }

  /// Request basic notification permission
  Future<PermissionResult> _requestNotificationPermission() async {
    try {
      // First check if already granted
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (isAllowed) {
        return PermissionResult(
          type: PermissionType.notification,
          granted: true,
          message: 'Notification permission already granted',
        );
      }

      // Request permission through awesome notifications with all required permissions
      final granted = await AwesomeNotifications().requestPermissionToSendNotifications(
        channelKey: null, // Request for all channels
        permissions: [
          NotificationPermission.Alert,
          NotificationPermission.Sound,
          NotificationPermission.Badge,
          NotificationPermission.Vibration,
          NotificationPermission.Light,
          NotificationPermission.CriticalAlert,
          NotificationPermission.OverrideDnD,
          NotificationPermission.PreciseAlarms,
          NotificationPermission.FullScreenIntent,
        ],
      );

      if (granted) {
        await _storage.write(NotificationConstants.permissionGrantedKey, true);
        return PermissionResult(
          type: PermissionType.notification,
          granted: true,
          message: 'Notification permission granted',
        );
      } else {
        return PermissionResult(
          type: PermissionType.notification,
          granted: false,
          message: 'Notification permission denied',
        );
      }

    } catch (e, stackTrace) {
      _logger.e('Error requesting notification permission', error: e, stackTrace: stackTrace);
      return PermissionResult(
        type: PermissionType.notification,
        granted: false,
        error: e.toString(),
      );
    }
  }

  /// Request exact alarm permission (Android only)
  Future<PermissionResult> _requestExactAlarmPermission() async {
    if (!Platform.isAndroid) {
      return PermissionResult(
        type: PermissionType.exactAlarm,
        granted: true,
        message: 'Not applicable on this platform',
      );
    }

    try {
      // Check if already granted
      final status = await Permission.scheduleExactAlarm.status;
      if (status.isGranted) {
        return PermissionResult(
          type: PermissionType.exactAlarm,
          granted: true,
          message: 'Exact alarm permission already granted',
        );
      }

      // Check Android version - exact alarms are only needed on Android 12+
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      if (androidInfo.version.sdkInt < 31) {
        _logger.d('Android version < 12, exact alarm permission not required');
        return PermissionResult(
          type: PermissionType.exactAlarm,
          granted: true,
          message: 'Exact alarm permission not required on this Android version',
        );
      }

      // Request permission
      final result = await Permission.scheduleExactAlarm.request();

      final granted = result.isGranted;
      if (granted) {
        await _storage.write(NotificationConstants.exactAlarmPermissionKey, true);
        _logger.i('Exact alarm permission granted - notifications will be precise');
      } else {
        _logger.w('Exact alarm permission denied - notifications may be delayed by system');
        // Still return success since the app can function without it
      }

      return PermissionResult(
        type: PermissionType.exactAlarm,
        granted: granted,
        message: granted
            ? 'Exact alarm permission granted - precise timing enabled'
            : 'Exact alarm permission denied - notifications may be delayed by system battery optimization',
      );

    } catch (e, stackTrace) {
      _logger.e('Error requesting exact alarm permission', error: e, stackTrace: stackTrace);
      return PermissionResult(
        type: PermissionType.exactAlarm,
        granted: false,
        error: e.toString(),
      );
    }
  }

  /// Request critical alert permission (iOS only)
  Future<PermissionResult> _requestCriticalAlertPermission() async {
    if (!Platform.isIOS) {
      return PermissionResult(
        type: PermissionType.criticalAlert,
        granted: true,
        message: 'Not applicable on this platform',
      );
    }

    try {
      // Request critical alerts through awesome notifications
      final granted = await AwesomeNotifications().requestPermissionToSendNotifications();

      if (granted) {
        await _storage.write(NotificationConstants.criticalAlertPermissionKey, true);
      }

      return PermissionResult(
        type: PermissionType.criticalAlert,
        granted: granted,
        message: granted
            ? 'Critical alert permission granted'
            : 'Critical alert permission denied',
      );

    } catch (e, stackTrace) {
      _logger.e('Error requesting critical alert permission', error: e, stackTrace: stackTrace);
      return PermissionResult(
        type: PermissionType.criticalAlert,
        granted: false,
        error: e.toString(),
      );
    }
  }

  /// Check exact alarm permission status
  Future<bool> _checkExactAlarmPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await Permission.scheduleExactAlarm.status;
      return status.isGranted;
    } catch (e) {
      _logger.w('Error checking exact alarm permission: $e');
      return false;
    }
  }

  /// Check critical alert permission status
  Future<bool> _checkCriticalAlertPermission() async {
    if (!Platform.isIOS) return true;

    try {
      // This is a simplified check - in practice, you might need platform-specific code
      return _storage.read(NotificationConstants.criticalAlertPermissionKey) ?? false;
    } catch (e) {
      _logger.w('Error checking critical alert permission: $e');
      return false;
    }
  }

  /// Cache permission status
  Future<void> _cachePermissionStatus(bool granted) async {
    await _storage.write(NotificationConstants.permissionGrantedKey, granted);
    await _storage.write(NotificationConstants.lastPermissionCheckKey, DateTime.now().toIso8601String());
  }

  /// Get cached permission status
  bool getCachedPermissionStatus() {
    return _storage.read(NotificationConstants.permissionGrantedKey) ?? false;
  }

  /// Check if permission check is needed (e.g., after app update)
  bool shouldCheckPermissions() {
    final lastCheck = _storage.read(NotificationConstants.lastPermissionCheckKey);
    if (lastCheck == null) return true;

    final lastCheckDate = DateTime.parse(lastCheck);
    final daysSinceLastCheck = DateTime.now().difference(lastCheckDate).inDays;

    // Check permissions every 7 days or after app updates
    return daysSinceLastCheck >= 7;
  }

  /// Open app settings for manual permission configuration
  Future<void> openAppSettings() async {
    try {
      await AwesomeNotifications().showNotificationConfigPage();
    } catch (e) {
      _logger.e('Error opening app settings: $e');
      // Fallback to system settings
      await openSystemSettings();
    }
  }

  /// Open system settings
  Future<void> openSystemSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      _logger.e('Error opening system settings: $e');
    }
  }

  /// Open exact alarm settings (Android only)
  Future<void> openExactAlarmSettings() async {
    if (!Platform.isAndroid) return;

    try {
      // Try to open exact alarm settings directly
      final result = await Permission.scheduleExactAlarm.request();
      if (!result.isGranted) {
        _logger.w('User denied exact alarm permission. Opening app settings...');
        await openAppSettings();
      }
    } catch (e) {
      _logger.e('Error opening exact alarm settings: $e');
      await openAppSettings();
    }
  }

  /// Get user-friendly explanation for exact alarm permission
  String getExactAlarmPermissionExplanation() {
    return '''
Exact Alarm Permission is required for precise prayer time notifications.

Without this permission:
• Notifications may be delayed by Android's battery optimization
• Prayer time alerts might not appear exactly on time
• The system may batch notifications together

To enable precise timing:
1. Go to Settings > Apps > [Your App Name]
2. Find "Alarms & reminders" or "Exact alarms"
3. Enable the permission

This ensures your prayer notifications arrive exactly on time.
''';
  }
}

/// Permission result model
class PermissionResult {
  final PermissionType type;
  final bool granted;
  final String? message;
  final String? error;

  const PermissionResult({
    required this.type,
    required this.granted,
    this.message,
    this.error,
  });

  @override
  String toString() {
    return 'PermissionResult(type: $type, granted: $granted, message: $message, error: $error)';
  }
}

/// Permission status model
class PermissionStatus {
  final bool hasNotificationPermission;
  final bool hasExactAlarmPermission;
  final bool hasCriticalAlertPermission;
  final String? message;
  final String? error;

  const PermissionStatus({
    required this.hasNotificationPermission,
    required this.hasExactAlarmPermission,
    required this.hasCriticalAlertPermission,
    this.message,
    this.error,
  });

  bool get hasAllPermissions =>
      hasNotificationPermission &&
      hasExactAlarmPermission &&
      hasCriticalAlertPermission;

  @override
  String toString() {
    return 'PermissionStatus(notification: $hasNotificationPermission, exactAlarm: $hasExactAlarmPermission, criticalAlert: $hasCriticalAlertPermission)';
  }
}

/// Permission types
enum PermissionType {
  notification,
  exactAlarm,
  criticalAlert,
}
