import 'notification_data.dart';
import '../constants/notification_constants.dart';

/// Prayer notification configuration model
class PrayerNotificationConfig {
  final String prayerName;
  final bool isNotified;
  final bool isPreNotified;
  final int? iqamahMinutes;
  final int soundIndex;
  final bool useDefaultSound;

  const PrayerNotificationConfig({
    required this.prayerName,
    this.isNotified = true,
    this.isPreNotified = false,
    this.iqamahMinutes,
    this.soundIndex = 0,
    this.useDefaultSound = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'prayerName': prayerName,
      'isNotified': isNotified,
      'isPreNotified': isPreNotified,
      'iqamahMinutes': iqamahMinutes,
      'soundIndex': soundIndex,
      'useDefaultSound': useDefaultSound,
    };
  }

  factory PrayerNotificationConfig.fromMap(Map<String, dynamic> map) {
    return PrayerNotificationConfig(
      prayerName: map['prayerName'] ?? '',
      isNotified: map['isNotified'] ?? true,
      isPreNotified: map['isPreNotified'] ?? false,
      iqamahMinutes: map['iqamahMinutes'],
      soundIndex: map['soundIndex'] ?? 0,
      useDefaultSound: map['useDefaultSound'] ?? false,
    );
  }

  PrayerNotificationConfig copyWith({
    String? prayerName,
    bool? isNotified,
    bool? isPreNotified,
    int? iqamahMinutes,
    int? soundIndex,
    bool? useDefaultSound,
  }) {
    return PrayerNotificationConfig(
      prayerName: prayerName ?? this.prayerName,
      isNotified: isNotified ?? this.isNotified,
      isPreNotified: isPreNotified ?? this.isPreNotified,
      iqamahMinutes: iqamahMinutes ?? this.iqamahMinutes,
      soundIndex: soundIndex ?? this.soundIndex,
      useDefaultSound: useDefaultSound ?? this.useDefaultSound,
    );
  }
}

/// Prayer times model
class PrayerTimes {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime? middleOfNight;
  final DateTime? lastThirdOfNight;

  const PrayerTimes({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    this.middleOfNight,
    this.lastThirdOfNight,
  });

  /// Get prayer time by name
  DateTime? getPrayerTime(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return fajr;
      case 'sunrise':
        return sunrise;
      case 'dhuhr':
        return dhuhr;
      case 'asr':
        return asr;
      case 'maghrib':
        return maghrib;
      case 'isha':
        return isha;
      case 'middle of the night':
        return middleOfNight;
      case 'last third of the night':
        return lastThirdOfNight;
      default:
        return null;
    }
  }

  /// Get all prayer times as map
  Map<String, DateTime> toMap() {
    final map = <String, DateTime>{
      NotificationConstants.fajr: fajr,
      NotificationConstants.sunrise: sunrise,
      NotificationConstants.dhuhr: dhuhr,
      NotificationConstants.asr: asr,
      NotificationConstants.maghrib: maghrib,
      NotificationConstants.isha: isha,
    };

    if (middleOfNight != null) {
      map[NotificationConstants.middleOfTheNight] = middleOfNight!;
    }
    if (lastThirdOfNight != null) {
      map[NotificationConstants.lastThirdOfTheNight] = lastThirdOfNight!;
    }

    return map;
  }

  factory PrayerTimes.fromMap(Map<String, dynamic> map) {
    return PrayerTimes(
      fajr: DateTime.parse(map['fajr']),
      sunrise: DateTime.parse(map['sunrise']),
      dhuhr: DateTime.parse(map['dhuhr']),
      asr: DateTime.parse(map['asr']),
      maghrib: DateTime.parse(map['maghrib']),
      isha: DateTime.parse(map['isha']),
      middleOfNight: map['middleOfNight'] != null 
          ? DateTime.parse(map['middleOfNight']) 
          : null,
      lastThirdOfNight: map['lastThirdOfNight'] != null 
          ? DateTime.parse(map['lastThirdOfNight']) 
          : null,
    );
  }
}

/// Prayer notification data extending base notification
class PrayerNotificationData extends NotificationData {
  final String prayerName;
  final PrayerNotificationType prayerNotificationType;

  const PrayerNotificationData({
    required super.id,
    required super.title,
    required super.body,
    required super.scheduledTime,
    required super.channelKey,
    required super.notificationType,
    required this.prayerName,
    required this.prayerNotificationType,
    super.payload,
    super.soundPath,
    super.customData,
    super.wakeUpScreen = true,
    super.fullScreenIntent = true,
    super.criticalAlert = true,
    super.actionButtons,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'prayerName': prayerName,
      'prayerNotificationType': prayerNotificationType.toString(),
    });
    return map;
  }

  factory PrayerNotificationData.fromMap(Map<String, dynamic> map) {
    return PrayerNotificationData(
      id: map['id'] ?? 0,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      scheduledTime: DateTime.parse(map['scheduledTime']),
      channelKey: map['channelKey'] ?? '',
      notificationType: map['notificationType'] ?? '',
      prayerName: map['prayerName'] ?? '',
      prayerNotificationType: PrayerNotificationType.values.firstWhere(
        (e) => e.toString() == map['prayerNotificationType'],
        orElse: () => PrayerNotificationType.main,
      ),
      payload: map['payload'],
      soundPath: map['soundPath'],
      customData: map['customData'] != null 
          ? Map<String, String>.from(map['customData']) 
          : null,
      wakeUpScreen: map['wakeUpScreen'] ?? true,
      fullScreenIntent: map['fullScreenIntent'] ?? true,
      criticalAlert: map['criticalAlert'] ?? true,
    );
  }
}

/// Types of prayer notifications
enum PrayerNotificationType {
  main,      // Main prayer time notification
  pre,       // Pre-prayer warning (15 minutes before)
  iqamah,    // Iqamah time notification
}

/// Athan sound types
enum AthanSoundType {
  athan1,
  athan2,
  athan3,
  athan4,
  bird,      // For sunrise
}
