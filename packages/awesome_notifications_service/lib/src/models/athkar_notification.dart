import 'notification_data.dart';

/// Athkar notification configuration model
class AthkarNotificationConfig {
  final AthkarType type;
  final bool isEnabled;
  final String time; // Time in HH:mm format
  final List<DhikrItem> dhikrItems;
  final int intervalMinutes;

  const AthkarNotificationConfig({
    required this.type,
    this.isEnabled = true,
    required this.time,
    this.dhikrItems = const [],
    this.intervalMinutes = 60, // Default 1 hour interval for dhikr
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'isEnabled': isEnabled,
      'time': time,
      'dhikrItems': dhikrItems.map((item) => item.toMap()).toList(),
      'intervalMinutes': intervalMinutes,
    };
  }

  factory AthkarNotificationConfig.fromMap(Map<String, dynamic> map) {
    return AthkarNotificationConfig(
      type: AthkarType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => AthkarType.morning,
      ),
      isEnabled: map['isEnabled'] ?? true,
      time: map['time'] ?? '06:00',
      dhikrItems: (map['dhikrItems'] as List<dynamic>?)
          ?.map((item) => DhikrItem.fromMap(item))
          .toList() ?? [],
      intervalMinutes: map['intervalMinutes'] ?? 60,
    );
  }

  AthkarNotificationConfig copyWith({
    AthkarType? type,
    bool? isEnabled,
    String? time,
    List<DhikrItem>? dhikrItems,
    int? intervalMinutes,
  }) {
    return AthkarNotificationConfig(
      type: type ?? this.type,
      isEnabled: isEnabled ?? this.isEnabled,
      time: time ?? this.time,
      dhikrItems: dhikrItems ?? this.dhikrItems,
      intervalMinutes: intervalMinutes ?? this.intervalMinutes,
    );
  }
}

/// Dhikr item model
class DhikrItem {
  final String title;
  final String subtitle;
  final String soundFile;
  final bool isEnabled;

  const DhikrItem({
    required this.title,
    required this.subtitle,
    required this.soundFile,
    this.isEnabled = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'subtitle': subtitle,
      'soundFile': soundFile,
      'isEnabled': isEnabled,
    };
  }

  factory DhikrItem.fromMap(Map<String, dynamic> map) {
    return DhikrItem(
      title: map['title'] ?? '',
      subtitle: map['subtitle'] ?? '',
      soundFile: map['soundFile'] ?? '',
      isEnabled: map['isEnabled'] ?? true,
    );
  }

  DhikrItem copyWith({
    String? title,
    String? subtitle,
    String? soundFile,
    bool? isEnabled,
  }) {
    return DhikrItem(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      soundFile: soundFile ?? this.soundFile,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }
}

/// Athkar notification data extending base notification
class AthkarNotificationData extends NotificationData {
  final AthkarType athkarType;
  final DhikrItem? dhikrItem;

  const AthkarNotificationData({
    required super.id,
    required super.title,
    required super.body,
    required super.scheduledTime,
    required super.channelKey,
    required super.notificationType,
    required this.athkarType,
    this.dhikrItem,
    super.payload,
    super.soundPath,
    super.customData,
    super.wakeUpScreen = false,
    super.fullScreenIntent = false,
    super.criticalAlert = false,
    super.actionButtons,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'athkarType': athkarType.toString(),
      'dhikrItem': dhikrItem?.toMap(),
    });
    return map;
  }

  factory AthkarNotificationData.fromMap(Map<String, dynamic> map) {
    return AthkarNotificationData(
      id: map['id'] ?? 0,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      scheduledTime: DateTime.parse(map['scheduledTime']),
      channelKey: map['channelKey'] ?? '',
      notificationType: map['notificationType'] ?? '',
      athkarType: AthkarType.values.firstWhere(
        (e) => e.toString() == map['athkarType'],
        orElse: () => AthkarType.morning,
      ),
      dhikrItem: map['dhikrItem'] != null 
          ? DhikrItem.fromMap(map['dhikrItem']) 
          : null,
      payload: map['payload'],
      soundPath: map['soundPath'],
      customData: map['customData'] != null 
          ? Map<String, String>.from(map['customData']) 
          : null,
      wakeUpScreen: map['wakeUpScreen'] ?? false,
      fullScreenIntent: map['fullScreenIntent'] ?? false,
      criticalAlert: map['criticalAlert'] ?? false,
    );
  }
}

/// Types of athkar notifications
enum AthkarType {
  morning,
  evening,
  dhikr,
}

/// Predefined dhikr items with their sound files
class PredefinedDhikr {
  static const List<DhikrItem> items = [
    DhikrItem(
      title: 'تسبيح الله',
      subtitle: 'سبحان الله',
      soundFile: 'sou_tasbhamd',
    ),
    DhikrItem(
      title: 'تحميد الله',
      subtitle: 'الحمد لله',
      soundFile: 'sou_tasbhamd',
    ),
    DhikrItem(
      title: 'تكبير الله',
      subtitle: 'الله أكبر',
      soundFile: 'sou_takbeer',
    ),
    DhikrItem(
      title: 'التوحيد',
      subtitle: 'لا إله إلا الله',
      soundFile: 'sou_tawheed',
    ),
    DhikrItem(
      title: 'الاستغفار',
      subtitle: 'أستغفر الله',
      soundFile: 'sou_esteghfar',
    ),
    DhikrItem(
      title: 'الصلاة على النبي',
      subtitle: 'اللهم صل على محمد',
      soundFile: 'sou_salah',
    ),
    DhikrItem(
      title: 'الباقيات الصالحات',
      subtitle: 'سبحان الله والحمد لله ولا إله إلا الله والله أكبر',
      soundFile: 'sou_baqyat',
    ),
    DhikrItem(
      title: 'الحوقلة',
      subtitle: 'لا حول ولا قوة إلا بالله',
      soundFile: 'sou_hawqalah',
    ),
    DhikrItem(
      title: 'دعاء الغم',
      subtitle: 'لا إله إلا الله العظيم الحليم',
      soundFile: 'sou_ghamm',
    ),
    DhikrItem(
      title: 'العدد',
      subtitle: 'اللهم أعني على ذكرك وشكرك وحسن عبادتك',
      soundFile: 'sou_adadd',
    ),
  ];

  /// Get dhikr item by sound file name
  static DhikrItem? getBySound(String soundFile) {
    try {
      return items.firstWhere((item) => item.soundFile == soundFile);
    } catch (e) {
      return null;
    }
  }

  /// Get all enabled dhikr items
  static List<DhikrItem> getEnabledItems() {
    return items.where((item) => item.isEnabled).toList();
  }
}
