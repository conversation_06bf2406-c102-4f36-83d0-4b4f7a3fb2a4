/// Base notification data model
class NotificationData {
  final int id;
  final String title;
  final String body;
  final DateTime scheduledTime;
  final String? payload;
  final String? soundPath;
  final String channelKey;
  final String notificationType;
  final Map<String, String>? customData;
  final bool wakeUpScreen;
  final bool fullScreenIntent;
  final bool criticalAlert;
  final List<NotificationActionButton>? actionButtons;

  const NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledTime,
    required this.channelKey,
    required this.notificationType,
    this.payload,
    this.soundPath,
    this.customData,
    this.wakeUpScreen = false,
    this.fullScreenIntent = false,
    this.criticalAlert = false,
    this.actionButtons,
  });

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'scheduledTime': scheduledTime.toIso8601String(),
      'payload': payload,
      'soundPath': soundPath,
      'channelKey': channelKey,
      'notificationType': notificationType,
      'customData': customData,
      'wakeUpScreen': wakeUpScreen,
      'fullScreenIntent': fullScreenIntent,
      'criticalAlert': criticalAlert,
    };
  }

  /// Create from map
  factory NotificationData.fromMap(Map<String, dynamic> map) {
    return NotificationData(
      id: map['id'] ?? 0,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      scheduledTime: DateTime.parse(map['scheduledTime']),
      payload: map['payload'],
      soundPath: map['soundPath'],
      channelKey: map['channelKey'] ?? '',
      notificationType: map['notificationType'] ?? '',
      customData: map['customData'] != null 
          ? Map<String, String>.from(map['customData']) 
          : null,
      wakeUpScreen: map['wakeUpScreen'] ?? false,
      fullScreenIntent: map['fullScreenIntent'] ?? false,
      criticalAlert: map['criticalAlert'] ?? false,
    );
  }

  /// Copy with new values
  NotificationData copyWith({
    int? id,
    String? title,
    String? body,
    DateTime? scheduledTime,
    String? payload,
    String? soundPath,
    String? channelKey,
    String? notificationType,
    Map<String, String>? customData,
    bool? wakeUpScreen,
    bool? fullScreenIntent,
    bool? criticalAlert,
    List<NotificationActionButton>? actionButtons,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      payload: payload ?? this.payload,
      soundPath: soundPath ?? this.soundPath,
      channelKey: channelKey ?? this.channelKey,
      notificationType: notificationType ?? this.notificationType,
      customData: customData ?? this.customData,
      wakeUpScreen: wakeUpScreen ?? this.wakeUpScreen,
      fullScreenIntent: fullScreenIntent ?? this.fullScreenIntent,
      criticalAlert: criticalAlert ?? this.criticalAlert,
      actionButtons: actionButtons ?? this.actionButtons,
    );
  }

  /// Enhanced string representation for debugging
  @override
  String toString() {
    return 'NotificationData('
        'id: $id, '
        'title: "$title", '
        'body: "${body.length > 50 ? '${body.substring(0, 50)}...' : body}", '
        'scheduledTime: ${scheduledTime.toIso8601String()}, '
        'channelKey: "$channelKey", '
        'notificationType: "$notificationType", '
        'payload: $payload, '
        'soundPath: $soundPath, '
        'wakeUpScreen: $wakeUpScreen, '
        'fullScreenIntent: $fullScreenIntent, '
        'criticalAlert: $criticalAlert, '
        'hasActionButtons: ${actionButtons?.isNotEmpty ?? false}'
        ')';
  }

  /// Enhanced equality comparison
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationData &&
        other.id == id &&
        other.title == title &&
        other.body == body &&
        other.scheduledTime == scheduledTime &&
        other.channelKey == channelKey &&
        other.notificationType == notificationType &&
        other.payload == payload &&
        other.soundPath == soundPath &&
        other.wakeUpScreen == wakeUpScreen &&
        other.fullScreenIntent == fullScreenIntent &&
        other.criticalAlert == criticalAlert;
  }

  /// Enhanced hash code
  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      body,
      scheduledTime,
      channelKey,
      notificationType,
      payload,
      soundPath,
      wakeUpScreen,
      fullScreenIntent,
      criticalAlert,
    );
  }
}

/// Notification action button model
class NotificationActionButton {
  final String key;
  final String label;
  final bool requiresUnlock;
  final bool showsUserInterface;
  final bool isDangerousOption;
  final bool autoDismissible;

  const NotificationActionButton({
    required this.key,
    required this.label,
    this.requiresUnlock = false,
    this.showsUserInterface = false,
    this.isDangerousOption = false,
    this.autoDismissible = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'label': label,
      'requiresUnlock': requiresUnlock,
      'showsUserInterface': showsUserInterface,
      'isDangerousOption': isDangerousOption,
      'autoDismissible': autoDismissible,
    };
  }

  factory NotificationActionButton.fromMap(Map<String, dynamic> map) {
    return NotificationActionButton(
      key: map['key'] ?? '',
      label: map['label'] ?? '',
      requiresUnlock: map['requiresUnlock'] ?? false,
      showsUserInterface: map['showsUserInterface'] ?? false,
      isDangerousOption: map['isDangerousOption'] ?? false,
      autoDismissible: map['autoDismissible'] ?? true,
    );
  }
}
