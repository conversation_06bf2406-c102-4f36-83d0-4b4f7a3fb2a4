/// Notification constants for the awesome notifications service
class NotificationConstants {
  // Notification IDs
  static const int prayerNotificationBaseId = 1000;
  static const int athkarNotificationBaseId = 2000;
  static const int systemNotificationBaseId = 3000;
  static const int firebaseNotificationBaseId = 4000;

  // Payload types
  static const String athanNotificationsPayload = 'athanNotifications';
  static const String soundNotificationsPayload = 'soundNotifications';
  static const String notificationsWarningPayload = 'notificationsWarning';
  static const String firebaseNotificationsPayload = 'firebaseNotifications';

  // Prayer names
  static const String fajr = 'Fajr';
  static const String sunrise = 'Sunrise';
  static const String dhuhr = 'Dhuhr';
  static const String asr = 'Asr';
  static const String maghrib = 'Maghrib';
  static const String isha = 'Isha';
  static const String middleOfTheNight = 'Middle Of The Night';
  static const String lastThirdOfTheNight = 'Last Third Of The Night';

  // Prayer list
  static const List<String> prayers = [fajr, sunrise, dhuhr, asr, maghrib, isha];
  static const List<String> sunnahPrayers = [middleOfTheNight, lastThirdOfTheNight];

  // Notification types
  static const String prayerTime = 'prayer_time';
  static const String prePrayerWarning = 'pre_prayer_warning';
  static const String iqamahTime = 'iqamah_time';
  static const String morningAthkar = 'morning_athkar';
  static const String eveningAthkar = 'evening_athkar';
  static const String dhikrReminder = 'dhikr_reminder';

  // Action IDs
  static const String dismissAction = 'DISMISS_ACTION';
  static const String stopAction = 'STOP_ACTION';
  static const String snoozeAction = 'SNOOZE_ACTION';

  // Storage keys
  static const String permissionGrantedKey = 'notification_permission_granted';
  static const String exactAlarmPermissionKey = 'exact_alarm_permission_granted';
  static const String criticalAlertPermissionKey = 'critical_alert_permission_granted';
  static const String lastPermissionCheckKey = 'last_permission_check';

  // Timing constants
  static const int prePrayerWarningMinutes = 15;
  static const int snoozeMinutes = 5;
  static const int maxNotificationDays = 7; // Schedule notifications for 7 days ahead

  // Sound file names (without extension)
  static const String defaultAthanSound = 'athan1';
  static const String birdSound = 'bird';
  static const String iqamaSound = 'iqama';
  static const String preAthanSound = 'pre_athan';

  // Athkar sound files
  static const String soundGhamm = 'sou_ghamm';
  static const String soundAdadd = 'sou_adadd';
  static const String soundTawheed = 'sou_tawheed';
  static const String soundTasbta3zeem = 'sou_tasbta3zeem';
  static const String soundTasbhamd = 'sou_tasbhamd';
  static const String soundSalah = 'sou_salah';
  static const String soundEsteghfar = 'sou_esteghfar';
  static const String soundBaqyat = 'sou_baqyat';
  static const String soundHawqalah = 'sou_hawqalah';
  static const String soundTakbeer = 'sou_takbeer';

  // App info
  static const String appName = 'صلواتي';
  static const String appNameEn = 'Salawati';
  static const String packageName = 'com.salawati.app';

  // Sound file constants (matching the original app)
  static const String kAthan1 = 'athan1';
  static const String kAthan1Short = 'athan1_short';
  static const String kAthan2 = 'athan2';
  static const String kAthan2Short = 'athan2_short';
  static const String kAthan3 = 'athan3';
  static const String kAthan3Short = 'athan3_short';
  static const String kAthan4 = 'athan4';
  static const String kAthan4Short = 'athan4_short';
  static const String kBird = 'bird';
  static const String kIqama = 'iqama';
  static const String kPreAthan = 'pre_athan';
}
